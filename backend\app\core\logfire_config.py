"""
Logfire Configuration and Instrumentation

This module handles Logfire configuration and automatic instrumentation
for FastAPI, asyncpg, SQLAlchemy, and other components.
"""

import logging
from typing import Any

import logfire
from fastapi import FastAPI

from app.core.config import get_settings
from app.core.logging import get_logger

# Get application settings and logger
settings = get_settings()
logger = get_logger(__name__)


def configure_logfire() -> None:
    """
    Configure Logfire with project settings and instrumentation.
    
    This function should be called during application startup.
    """
    if not settings.LOGFIRE_TOKEN:
        logger.warning("Logfire token not provided, skipping Logfire configuration")
        return

    try:
        # Configure Logfire with project settings
        logfire.configure(
            token=settings.LOGFIRE_TOKEN,
            project_name=settings.LOGFIRE_PROJECT_NAME,
            service_name=settings.LOGFIRE_SERVICE_NAME,
            service_version=settings.LOGFIRE_SERVICE_VERSION,
            environment=settings.logfire_environment,
            console=False,  # We handle console logging separately
        )

        logger.info(
            "Logfire configured successfully",
            extra={
                "project_name": settings.LOGFIRE_PROJECT_NAME,
                "service_name": settings.LOGFIRE_SERVICE_NAME,
                "service_version": settings.LOGFIRE_SERVICE_VERSION,
                "environment": settings.logfire_environment,
            }
        )

        # Configure automatic instrumentation
        _configure_instrumentation()

    except Exception as e:
        logger.error(
            "Failed to configure Logfire",
            extra={"error": str(e), "error_type": type(e).__name__}
        )
        raise


def _configure_instrumentation() -> None:
    """Configure automatic instrumentation for various libraries."""
    
    # Instrument asyncpg for database query tracing
    if settings.LOGFIRE_INSTRUMENT_ASYNCPG:
        try:
            logfire.instrument_asyncpg()
            logger.info("Asyncpg instrumentation enabled")
        except Exception as e:
            logger.warning(
                "Failed to instrument asyncpg",
                extra={"error": str(e)}
            )

    # Instrument SQLAlchemy for ORM query tracing
    if settings.LOGFIRE_INSTRUMENT_SQLALCHEMY:
        try:
            logfire.instrument_sqlalchemy()
            logger.info("SQLAlchemy instrumentation enabled")
        except Exception as e:
            logger.warning(
                "Failed to instrument SQLAlchemy",
                extra={"error": str(e)}
            )

    # Instrument HTTPX for HTTP client tracing
    if settings.LOGFIRE_INSTRUMENT_HTTPX:
        try:
            logfire.instrument_httpx()
            logger.info("HTTPX instrumentation enabled")
        except Exception as e:
            logger.warning(
                "Failed to instrument HTTPX",
                extra={"error": str(e)}
            )

    # Instrument requests for HTTP client tracing
    if settings.LOGFIRE_INSTRUMENT_REQUESTS:
        try:
            logfire.instrument_requests()
            logger.info("Requests instrumentation enabled")
        except Exception as e:
            logger.warning(
                "Failed to instrument requests",
                extra={"error": str(e)}
            )


def instrument_fastapi(app: FastAPI) -> None:
    """
    Instrument FastAPI application with Logfire.
    
    Args:
        app: FastAPI application instance
    """
    if not settings.LOGFIRE_TOKEN or not settings.LOGFIRE_INSTRUMENT_FASTAPI:
        logger.info("FastAPI instrumentation disabled")
        return

    try:
        # Configure FastAPI instrumentation options
        instrumentation_config = {
            "capture_headers": settings.LOGFIRE_CAPTURE_HEADERS,
            "capture_query_params": settings.LOGFIRE_CAPTURE_QUERY_PARAMETERS,
        }

        # Instrument FastAPI
        logfire.instrument_fastapi(app, **instrumentation_config)
        
        logger.info(
            "FastAPI instrumentation enabled",
            extra=instrumentation_config
        )

    except Exception as e:
        logger.error(
            "Failed to instrument FastAPI",
            extra={"error": str(e), "error_type": type(e).__name__}
        )


def log_application_startup() -> None:
    """Log application startup event with context."""
    if not settings.LOGFIRE_TOKEN:
        return

    try:
        logfire.info(
            "InsightStream Backend starting up",
            service_name=settings.LOGFIRE_SERVICE_NAME,
            service_version=settings.LOGFIRE_SERVICE_VERSION,
            environment=settings.logfire_environment,
            debug_mode=settings.DEBUG,
        )
    except Exception as e:
        logger.warning(
            "Failed to log startup event to Logfire",
            extra={"error": str(e)}
        )


def log_application_shutdown() -> None:
    """Log application shutdown event with context."""
    if not settings.LOGFIRE_TOKEN:
        return

    try:
        logfire.info(
            "InsightStream Backend shutting down",
            service_name=settings.LOGFIRE_SERVICE_NAME,
            service_version=settings.LOGFIRE_SERVICE_VERSION,
            environment=settings.logfire_environment,
        )
    except Exception as e:
        logger.warning(
            "Failed to log shutdown event to Logfire",
            extra={"error": str(e)}
        )


def log_database_initialization(success: bool, error: str | None = None) -> None:
    """
    Log database initialization event.
    
    Args:
        success: Whether initialization was successful
        error: Error message if initialization failed
    """
    if not settings.LOGFIRE_TOKEN:
        return

    try:
        if success:
            logfire.info(
                "Database initialized successfully",
                database_url_masked=str(settings.DATABASE_URL).split("@")[1] if settings.DATABASE_URL and "@" in str(settings.DATABASE_URL) else "unknown",
                pool_size=settings.DATABASE_POOL_SIZE,
                max_overflow=settings.DATABASE_MAX_OVERFLOW,
            )
        else:
            logfire.error(
                "Database initialization failed",
                error=error,
                database_url_masked=str(settings.DATABASE_URL).split("@")[1] if settings.DATABASE_URL and "@" in str(settings.DATABASE_URL) else "unknown",
            )
    except Exception as e:
        logger.warning(
            "Failed to log database initialization to Logfire",
            extra={"error": str(e)}
        )


def create_span(name: str, **attributes: Any) -> Any:
    """
    Create a Logfire span for custom tracing.
    
    Args:
        name: Span name
        **attributes: Additional span attributes
        
    Returns:
        Logfire span context manager
    """
    if not settings.LOGFIRE_TOKEN:
        # Return a no-op context manager if Logfire is not configured
        from contextlib import nullcontext
        return nullcontext()

    try:
        return logfire.span(name, **attributes)
    except Exception as e:
        logger.warning(
            "Failed to create Logfire span",
            extra={"span_name": name, "error": str(e)}
        )
        from contextlib import nullcontext
        return nullcontext()


def log_user_action(user_id: str, action: str, **context: Any) -> None:
    """
    Log user action with Logfire.
    
    Args:
        user_id: User identifier
        action: Action performed
        **context: Additional context data
    """
    if not settings.LOGFIRE_TOKEN:
        return

    try:
        logfire.info(
            f"User action: {action}",
            user_id=user_id,
            action=action,
            user_action=True,
            **context
        )
    except Exception as e:
        logger.warning(
            "Failed to log user action to Logfire",
            extra={"user_id": user_id, "action": action, "error": str(e)}
        )


def log_performance_metric(operation: str, duration: float, **context: Any) -> None:
    """
    Log performance metric with Logfire.
    
    Args:
        operation: Operation name
        duration: Duration in seconds
        **context: Additional context data
    """
    if not settings.LOGFIRE_TOKEN:
        return

    try:
        logfire.info(
            f"Performance: {operation} completed in {duration:.3f}s",
            operation=operation,
            duration=duration,
            performance_metric=True,
            **context
        )
    except Exception as e:
        logger.warning(
            "Failed to log performance metric to Logfire",
            extra={"operation": operation, "duration": duration, "error": str(e)}
        )

"""
Logging Configuration

This module sets up comprehensive logging for the InsightStream backend using
Logfire for structured logging and Rich for beautiful terminal output.
"""

import logging
from typing import Any, Dict

import logfire
from rich.console import Console
from rich.logging import <PERSON><PERSON>andler
from rich.traceback import install

from app.core.config import get_settings

# Get application settings
settings = get_settings()

# Initialize Rich console
console = Console()

# Install Rich traceback handler for better error formatting
install(show_locals=True)


class LogfireHandler(logging.Handler):
    """Custom logging handler that sends logs to Logfire."""

    def emit(self, record: logging.LogRecord) -> None:
        """Emit a log record to Logfire."""
        try:
            # Extract extra data from the log record
            extra_data = {}
            for key, value in record.__dict__.items():
                if key not in {
                    "name",
                    "msg",
                    "args",
                    "levelname",
                    "levelno",
                    "pathname",
                    "filename",
                    "module",
                    "lineno",
                    "funcName",
                    "created",
                    "msecs",
                    "relativeCreated",
                    "thread",
                    "threadName",
                    "processName",
                    "process",
                    "getMessage",
                    "exc_info",
                    "exc_text",
                    "stack_info",
                    "message",
                }:
                    extra_data[key] = value

            # Format the message
            message = record.getMessage()

            # Send to Logfire based on log level
            if record.levelno >= logging.ERROR:
                logfire.error(message, **extra_data)
            elif record.levelno >= logging.WARNING:
                logfire.warning(message, **extra_data)
            elif record.levelno >= logging.INFO:
                logfire.info(message, **extra_data)
            else:
                logfire.debug(message, **extra_data)

        except Exception:
            # Don't let logging errors break the application
            pass


def setup_logging() -> None:
    """
    Set up application logging configuration.

    Configures both Rich terminal logging and Logfire structured logging.
    """
    # Clear any existing handlers
    root_logger = logging.getLogger()
    root_logger.handlers.clear()

    # Set log level
    log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)
    root_logger.setLevel(log_level)

    # Create Rich handler for terminal output
    rich_handler = RichHandler(
        console=console,
        show_time=True,
        show_level=True,
        show_path=True,
        markup=True,
        rich_tracebacks=True,
        tracebacks_show_locals=True,
    )
    rich_handler.setLevel(log_level)

    # Create formatter for Rich handler
    rich_formatter = logging.Formatter(
        fmt="%(message)s",
        datefmt="[%X]",
    )
    rich_handler.setFormatter(rich_formatter)

    # Add Rich handler to root logger
    root_logger.addHandler(rich_handler)

    # Add Logfire handler if token is available
    if settings.LOGFIRE_TOKEN:
        logfire_handler = LogfireHandler()
        logfire_handler.setLevel(log_level)
        root_logger.addHandler(logfire_handler)

    # Configure specific loggers
    configure_logger("uvicorn", log_level)
    configure_logger("uvicorn.access", logging.WARNING)  # Reduce access log noise
    configure_logger("sqlalchemy.engine", logging.WARNING)  # Reduce SQL noise
    configure_logger("alembic", logging.INFO)
    configure_logger("celery", logging.INFO)
    configure_logger("redis", logging.WARNING)

    # Configure application loggers
    configure_logger("app", log_level)
    configure_logger("app.core", log_level)
    configure_logger("app.api", log_level)
    configure_logger("app.services", log_level)
    configure_logger("app.models", log_level)

    console.print("✅ [bold green]Logging configuration completed[/bold green]")


def configure_logger(name: str, level: int) -> logging.Logger:
    """
    Configure a specific logger.

    Args:
        name: Logger name
        level: Log level

    Returns:
        logging.Logger: Configured logger instance
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance for a specific module.

    Args:
        name: Logger name (usually __name__)

    Returns:
        logging.Logger: Logger instance
    """
    return logging.getLogger(name)


class StructuredLogger:
    """
    Structured logger wrapper for consistent logging across the application.
    """

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.name = name

    def debug(self, message: str, **kwargs: Any) -> None:
        """Log debug message with structured data."""
        self.logger.debug(message, extra=kwargs)

    def info(self, message: str, **kwargs: Any) -> None:
        """Log info message with structured data."""
        self.logger.info(message, extra=kwargs)

    def warning(self, message: str, **kwargs: Any) -> None:
        """Log warning message with structured data."""
        self.logger.warning(message, extra=kwargs)

    def error(self, message: str, **kwargs: Any) -> None:
        """Log error message with structured data."""
        self.logger.error(message, extra=kwargs)

    def critical(self, message: str, **kwargs: Any) -> None:
        """Log critical message with structured data."""
        self.logger.critical(message, extra=kwargs)

    def exception(self, message: str, **kwargs: Any) -> None:
        """Log exception with traceback and structured data."""
        self.logger.exception(message, extra=kwargs)


def log_function_call(
    func_name: str, args: tuple = (), kwargs: Dict[str, Any] = None
) -> None:
    """
    Log function call with parameters.

    Args:
        func_name: Name of the function being called
        args: Function arguments
        kwargs: Function keyword arguments
    """
    logger = get_logger("app.function_calls")
    logger.debug(
        f"Function called: {func_name}",
        extra={
            "function": func_name,
            "args_count": len(args),
            "kwargs_keys": list(kwargs.keys()) if kwargs else [],
        },
    )


def log_performance(operation: str, duration: float, **context: Any) -> None:
    """
    Log performance metrics.

    Args:
        operation: Name of the operation
        duration: Duration in seconds
        **context: Additional context data
    """
    logger = get_logger("app.performance")
    logger.info(
        f"Performance: {operation} completed in {duration:.3f}s",
        extra={
            "operation": operation,
            "duration": duration,
            "performance_metric": True,
            **context,
        },
    )


def log_user_action(user_id: str, action: str, **context: Any) -> None:
    """
    Log user actions for analytics and auditing.

    Args:
        user_id: User identifier
        action: Action performed
        **context: Additional context data
    """
    logger = get_logger("app.user_actions")
    logger.info(
        f"User action: {action}",
        extra={
            "user_id": user_id,
            "action": action,
            "user_action": True,
            **context,
        },
    )


def log_api_request(
    method: str, path: str, status_code: int, duration: float, **context: Any
) -> None:
    """
    Log API request details.

    Args:
        method: HTTP method
        path: Request path
        status_code: Response status code
        duration: Request duration in seconds
        **context: Additional context data
    """
    logger = get_logger("app.api_requests")
    logger.info(
        f"API {method} {path} - {status_code} ({duration:.3f}s)",
        extra={
            "method": method,
            "path": path,
            "status_code": status_code,
            "duration": duration,
            "api_request": True,
            **context,
        },
    )

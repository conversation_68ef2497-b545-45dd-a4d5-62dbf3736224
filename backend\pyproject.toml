[project]
name = "insightstream-backend"
version = "1.0.0"
description = "InsightStream - Unlock the Knowledge Within Video"
authors = [
    {name = "InsightStream Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "alembic>=1.16.1",
    "asyncpg>=0.30.0",
    "fastapi[standard]>=0.115.12",
    "logfire>=3.16.1",
    "pydantic>=2.11.5",
    "pydantic-settings>=2.9.1",
    "python-jose[cryptography]>=3.5.0",
    "rich>=14.0.0",
    "sqlmodel>=0.0.24",
    "uvicorn[standard]>=0.30.0",
]

# "fastapi>=0.104.0",
#     "uvicorn[standard]>=0.24.0",
#     "sqlmodel>=0.0.14",
#     "alembic>=1.12.0",
#     "asyncpg>=0.29.0",
#     "psycopg2-binary>=2.9.7",
#     "redis>=5.0.0",
#     "celery>=5.3.0",
#     "pydantic>=2.5.0",
#     "pydantic-settings>=2.1.0",
#     "python-dotenv>=1.0.0",
#     "python-multipart>=0.0.6",
#     "python-jose[cryptography]>=3.3.0",
#     "passlib[bcrypt]>=1.7.4",
#     "logfire>=0.15.0",
#     "rich>=13.7.0",
#     "httpx>=0.25.0",
#     "aiofiles>=23.2.1",

# [project.optional-dependencies]
# dev = [
#     # Development dependencies are managed via dev-requirements.txt
#     # Use: uv add --dev -r dev-requirements.txt to install current versions
# ]

# [build-system]
# requires = ["hatchling"]
# build-backend = "hatchling.build"

# [tool.ruff]
# target-version = "py311"
# line-length = 88
# select = [
#     "E",   # pycodestyle errors
#     "W",   # pycodestyle warnings
#     "F",   # pyflakes
#     "I",   # isort
#     "B",   # flake8-bugbear
#     "C4",  # flake8-comprehensions
#     "UP",  # pyupgrade
#     "ARG", # flake8-unused-arguments
#     "SIM", # flake8-simplify
#     "TID", # flake8-tidy-imports
#     "Q",   # flake8-quotes
#     "FLY", # flynt
#     "PERF", # perflint
#     "RUF", # ruff-specific rules
# ]
# ignore = [
#     "E501",  # line too long, handled by black
#     "B008",  # do not perform function calls in argument defaults
#     "C901",  # too complex
#     "ARG002", # unused method argument
#     "ARG001", # unused function argument
#     "TID252", # prefer absolute imports
# ]

# [tool.ruff.format]
# quote-style = "double"
# indent-style = "space"
# skip-magic-trailing-comma = false
# line-ending = "auto"

# [tool.ruff.isort]
# known-first-party = ["app"]
# force-sort-within-sections = true
# split-on-trailing-comma = true

# [tool.ruff.per-file-ignores]
# "__init__.py" = ["F401"]
# "tests/**/*" = ["ARG", "FBT", "PLR2004", "S101", "TID252"]

# [tool.mypy]
# python_version = "3.11"
# check_untyped_defs = true
# disallow_any_generics = true
# disallow_incomplete_defs = true
# disallow_untyped_defs = true
# no_implicit_optional = true
# warn_redundant_casts = true
# warn_unused_ignores = true
# warn_return_any = true
# strict_equality = true
# strict_concatenate = true

# [[tool.mypy.overrides]]
# module = [
#     "celery.*",
#     "redis.*",
#     "qdrant_client.*",
#     "spacy.*",
#     "networkx.*",
#     "neo4j.*",
#     "yt_dlp.*",
#     "youtube_transcript_api.*",
#     "whisper.*",
#     "logfire.*",
# ]
# ignore_missing_imports = true

# [tool.pytest.ini_options]
# minversion = "7.0"
# addopts = [
#     "--strict-markers",
#     "--strict-config",
#     "--disable-warnings",
#     "--tb=short",
#     "-ra",
# ]
# testpaths = ["tests"]
# filterwarnings = [
#     "ignore::DeprecationWarning",
#     "ignore::PendingDeprecationWarning",
# ]
# markers = [
#     "slow: marks tests as slow (deselect with '-m \"not slow\"')",
#     "integration: marks tests as integration tests",
#     "unit: marks tests as unit tests",
#     "e2e: marks tests as end-to-end tests",
# ]

# [tool.coverage.run]
# source = ["app"]
# omit = [
#     "*/tests/*",
#     "*/migrations/*",
#     "*/venv/*",
#     "*/.venv/*",
#     "*/node_modules/*",
# ]

# [tool.coverage.report]
# exclude_lines = [
#     "pragma: no cover",
#     "def __repr__",
#     "if self.debug:",
#     "if settings.DEBUG",
#     "raise AssertionError",
#     "raise NotImplementedError",
#     "if 0:",
#     "if __name__ == .__main__.:",
#     "class .*\\bProtocol\\):",
#     "@(abc\\.)?abstractmethod",
# ]
# show_missing = true
# skip_covered = false

# [tool.coverage.html]
# directory = "htmlcov"

# # Pre-commit configuration (create .pre-commit-config.yaml separately)
# # This section is for reference only

# [tool.alembic]
# script_location = "alembic"
# prepend_sys_path = ["."]
# version_path_separator = "os"
# sqlalchemy.url = "driver://user:pass@localhost/dbname"

# [tool.celery]
# broker_url = "redis://localhost:6379/0"
# result_backend = "redis://localhost:6379/0"
# task_serializer = "json"
# accept_content = ["json"]
# result_serializer = "json"
# timezone = "UTC"
# enable_utc = true

# [tool.logfire]
# project_name = "insightstream"
# environment = "development"

#!/usr/bin/env python3
"""
Test script for Logfire configuration

This script tests the Logfire configuration without starting the full FastAPI application.
Run this to verify that Logfire is properly configured.
"""

import sys
from pathlib import Path

# Add the app directory to the Python path
root_dir = Path(__file__).parent.parent
sys.path.insert(0, str(root_dir))

import asyncio

from app.core.config import get_settings
from app.core.logfire_config import (
    configure_logfire,
    create_span,
    log_application_startup,
    log_performance_metric,
    log_user_action,
)
from app.core.logging import get_logger, setup_logging


async def test_logfire_configuration():
    """Test the Logfire configuration."""
    print("🧪 Testing Logfire Configuration...")

    # Setup logging first
    setup_logging()
    logger = get_logger(__name__)

    # Get settings
    settings = get_settings()

    print(f"📋 Environment: {settings.ENVIRONMENT}")
    print(f"📋 Logfire Token: {'✅ Set' if settings.LOGFIRE_TOKEN else '❌ Not Set'}")
    print(f"📋 Project Name: {settings.LOGFIRE_PROJECT_NAME}")
    print(f"📋 Service Name: {settings.LOGFIRE_SERVICE_NAME}")
    print(f"📋 Service Version: {settings.LOGFIRE_SERVICE_VERSION}")
    print(f"📋 Logfire Environment: {settings.logfire_environment}")

    # Test configuration
    try:
        configure_logfire()
        print("✅ Logfire configuration successful")
    except Exception as e:
        print(f"❌ Logfire configuration failed: {e}")
        return False

    # Test logging functions
    try:
        log_application_startup()
        print("✅ Application startup logging successful")
    except Exception as e:
        print(f"❌ Application startup logging failed: {e}")

    # Test span creation
    try:
        with create_span("test_span", test_attribute="test_value"):
            logger.info("Testing span creation")
        print("✅ Span creation successful")
    except Exception as e:
        print(f"❌ Span creation failed: {e}")

    # Test user action logging
    try:
        log_user_action(
            user_id="test_user_123",
            action="test_action",
            additional_context="test_data",
        )
        print("✅ User action logging successful")
    except Exception as e:
        print(f"❌ User action logging failed: {e}")

    # Test performance metric logging
    try:
        log_performance_metric(
            operation="test_operation", duration=0.123, test_metric="test_value"
        )
        print("✅ Performance metric logging successful")
    except Exception as e:
        print(f"❌ Performance metric logging failed: {e}")

    print("🎉 Logfire configuration test completed!")
    return True


def test_instrumentation_flags():
    """Test instrumentation configuration flags."""
    print("\n🔧 Testing Instrumentation Flags...")

    settings = get_settings()

    flags = {
        "FastAPI": settings.LOGFIRE_INSTRUMENT_FASTAPI,
        "AsyncPG": settings.LOGFIRE_INSTRUMENT_ASYNCPG,
        "SQLModel": settings.LOGFIRE_INSTRUMENT_SQLMODEL,
        "HTTPX": settings.LOGFIRE_INSTRUMENT_HTTPX,
        "Requests": settings.LOGFIRE_INSTRUMENT_REQUESTS,
    }

    for name, enabled in flags.items():
        status = "✅ Enabled" if enabled else "❌ Disabled"
        print(f"📋 {name} Instrumentation: {status}")

    capture_flags = {
        "Headers": settings.LOGFIRE_CAPTURE_HEADERS,
        "Query Parameters": settings.LOGFIRE_CAPTURE_QUERY_PARAMETERS,
    }

    for name, enabled in capture_flags.items():
        status = "✅ Enabled" if enabled else "❌ Disabled"
        print(f"📋 Capture {name}: {status}")


def main():
    """Main test function."""
    print("🚀 Starting Logfire Configuration Test\n")

    # Test instrumentation flags
    test_instrumentation_flags()

    # Test logfire configuration
    success = asyncio.run(test_logfire_configuration())

    if success:
        print("\n🎯 All tests passed! Logfire is properly configured.")
        return 0
    else:
        print("\n❌ Some tests failed. Check the configuration.")
        return 1


if __name__ == "__main__":
    exit(main())

"""
Application Configuration

This module contains all configuration settings for the InsightStream backend,
using Pydantic Settings for environment variable management and validation.
"""

from functools import lru_cache

from pydantic import Field, PostgresDsn
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Application Settings
    APP_NAME: str = Field(default="InsightStream", description="Application name")
    APP_MOTTO: str = Field(
        default="Unlock the Knowledge Within Video", description="Application motto"
    )
    VERSION: str = Field(default="0.1.0", description="Application version")
    ENVIRONMENT: str = Field(
        default="development",
        description="Environment (development/production/testing)",
    )
    DEBUG: bool = Field(default=True, description="Debug mode")

    # Server Settings
    HOST: str = Field(default="0.0.0.0", description="Server host")
    PORT: int = Field(default=8000, description="Server port")

    # Security Settings
    SECRET_KEY: str = Field(description="Secret key for JWT tokens")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30, description="Access token expiration in minutes"
    )
    REFRESH_TOKEN_EXPIRE_MINUTES: int = Field(
        default=10080, description="Refresh token expiration in minutes (7 days)"
    )
    ALGORITHM: str = Field(default="HS256", description="JWT algorithm")

    # Database Settings
    DATABASE_URL: PostgresDsn | None = Field(
        default=None, description="PostgreSQL database URL"
    )
    DATABASE_ECHO: bool = Field(default=True, description="Echo SQL queries")
    DATABASE_POOL_SIZE: int = Field(
        default=10, description="Database connection pool size"
    )
    DATABASE_MAX_OVERFLOW: int = Field(
        default=20, description="Database max overflow connections"
    )
    DATABASE_POOL_TIMEOUT: int = Field(default=30, description="Database pool timeout")
    DATABASE_POOL_RECYCLE: int = Field(
        default=3600, description="Database pool recycle time"
    )

    # CORS Settings
    CORS_ORIGINS: list[str] = Field(
        default=[
            "http://localhost:3000",
            "http://localhost:8080",
            "http://127.0.0.1:3000",
        ],
        description="Allowed CORS origins",
    )
    ALLOWED_HOSTS: list[str] = Field(
        default=["localhost", "127.0.0.1"], description="Allowed hosts for production"
    )

    # Logging Settings
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOGFIRE_TOKEN: str | None = Field(
        default=None, description="Logfire token for monitoring"
    )
    LOGFIRE_PROJECT_NAME: str = Field(
        default="insightstream", description="Logfire project name"
    )
    LOGFIRE_SERVICE_NAME: str = Field(
        default="insightstream-backend", description="Logfire service name"
    )
    LOGFIRE_SERVICE_VERSION: str = Field(
        default="0.1.0", description="Logfire service version"
    )
    LOGFIRE_ENVIRONMENT: str | None = Field(
        default=None, description="Logfire environment (defaults to ENVIRONMENT)"
    )

    # Logfire Feature Flags
    LOGFIRE_INSTRUMENT_FASTAPI: bool = Field(
        default=True, description="Enable FastAPI instrumentation"
    )
    LOGFIRE_INSTRUMENT_ASYNCPG: bool = Field(
        default=True, description="Enable asyncpg instrumentation"
    )
    LOGFIRE_INSTRUMENT_SQLMODEL: bool = Field(
        default=True, description="Enable SQLModel/SQLAlchemy instrumentation"
    )
    LOGFIRE_INSTRUMENT_HTTPX: bool = Field(
        default=True, description="Enable HTTPX instrumentation"
    )
    LOGFIRE_INSTRUMENT_REQUESTS: bool = Field(
        default=True, description="Enable requests instrumentation"
    )
    LOGFIRE_CAPTURE_HEADERS: bool = Field(
        default=False, description="Capture HTTP headers in traces"
    )
    LOGFIRE_CAPTURE_QUERY_PARAMETERS: bool = Field(
        default=False, description="Capture query parameters in traces"
    )

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=True, extra="ignore"
    )

    @property
    def database_url_sync(self) -> str:
        """Get synchronous database URL for Alembic."""
        if self.DATABASE_URL:
            return str(self.DATABASE_URL).replace(
                "postgresql+asyncpg://", "postgresql://"
            )
        return "postgresql://user:password@localhost/insightstream"

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT.lower() == "production"

    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT.lower() == "development"

    @property
    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return self.ENVIRONMENT.lower() == "testing"

    @property
    def logfire_environment(self) -> str:
        """Get the effective logfire environment."""
        return self.LOGFIRE_ENVIRONMENT or self.ENVIRONMENT


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached application settings.

    Returns:
        Settings: Application configuration instance
    """
    return Settings()


# Export commonly used settings
settings = get_settings()

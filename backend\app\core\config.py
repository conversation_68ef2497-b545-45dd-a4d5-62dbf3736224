"""
Application Configuration

This module contains all configuration settings for the InsightStream backend,
using Pydantic Settings for environment variable management and validation.
"""

# from functools import lru_cache
# from pyexpat import model

# from pydantic import (
#     AnyHttpUrl,
#     BaseSettings,
#     EmailStr,
#     Field,
#     PostgresDsn,
#     RedisDsn,
#     validator,
# )
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Application Settings
    APP_NAME: str
    APP_MOTTO: str
    VERSION: str
    ENVIRONMENT: str
    # DEBUG: bool = Field(default=True, env="DEBUG")

    # Security Settings
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int
    REFRESH_TOKEN_EXPIRE_MINUTES: int
    ALGORITHM: str

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

    # # Server Settings
    # HOST: str = Field(default="0.0.0.0", env="HOST")
    # PORT: int = Field(default=8000, env="PORT")
    # WORKERS: int = Field(default=1, env="WORKERS")

    # # CORS Settings
    # CORS_ORIGINS: List[AnyHttpUrl] = Field(
    #     default=["http://localhost:3000", "http://localhost:3001"], env="CORS_ORIGINS"
    # )
    # ALLOWED_HOSTS: List[str] = Field(
    #     default=["localhost", "127.0.0.1"], env="ALLOWED_HOSTS"
    # )

    # # Database Settings
    # DATABASE_URL: Optional[PostgresDsn] = Field(default=None, env="DATABASE_URL")
    # DATABASE_ECHO: bool = Field(default=False, env="DATABASE_ECHO")
    # DATABASE_POOL_SIZE: int = Field(default=10, env="DATABASE_POOL_SIZE")
    # DATABASE_MAX_OVERFLOW: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")

    # # Redis Settings
    # REDIS_URL: Optional[RedisDsn] = Field(
    #     default="redis://localhost:6379/0", env="REDIS_URL"
    # )

    # # Celery Settings
    # CELERY_BROKER_URL: Optional[RedisDsn] = Field(
    #     default="redis://localhost:6379/1", env="CELERY_BROKER_URL"
    # )
    # CELERY_RESULT_BACKEND: Optional[RedisDsn] = Field(
    #     default="redis://localhost:6379/1", env="CELERY_RESULT_BACKEND"
    # )

    # # AI Service API Keys
    # AGNO_API_KEY: Optional[str] = Field(default=None, env="AGNO_API_KEY")
    # GROQ_API_KEY: Optional[str] = Field(default=None, env="GROQ_API_KEY")

    # # Logging and Monitoring
    # LOGFIRE_TOKEN: Optional[str] = Field(default=None, env="LOGFIRE_TOKEN")
    # SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    # LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")

    # # File Storage Settings
    # AWS_ACCESS_KEY_ID: Optional[str] = Field(default=None, env="AWS_ACCESS_KEY_ID")
    # AWS_SECRET_ACCESS_KEY: Optional[str] = Field(
    #     default=None, env="AWS_SECRET_ACCESS_KEY"
    # )
    # AWS_REGION: str = Field(default="us-east-1", env="AWS_REGION")
    # S3_BUCKET_NAME: Optional[str] = Field(default=None, env="S3_BUCKET_NAME")

    # # Video Processing Settings
    # MAX_VIDEO_DURATION: int = Field(default=7200, env="MAX_VIDEO_DURATION")  # 2 hours
    # MAX_FILE_SIZE: int = Field(default=500 * 1024 * 1024, env="MAX_FILE_SIZE")  # 500MB
    # SUPPORTED_VIDEO_FORMATS: List[str] = Field(
    #     default=["mp4", "avi", "mov", "mkv", "webm"], env="SUPPORTED_VIDEO_FORMATS"
    # )


#     # Rate Limiting
#     RATE_LIMIT_PER_MINUTE: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
#     RATE_LIMIT_PER_HOUR: int = Field(default=1000, env="RATE_LIMIT_PER_HOUR")

#     # Email Settings (for notifications)
#     SMTP_TLS: bool = Field(default=True, env="SMTP_TLS")
#     SMTP_PORT: Optional[int] = Field(default=587, env="SMTP_PORT")
#     SMTP_HOST: Optional[str] = Field(default=None, env="SMTP_HOST")
#     SMTP_USER: Optional[str] = Field(default=None, env="SMTP_USER")
#     SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
#     EMAILS_FROM_EMAIL: Optional[EmailStr] = Field(default=None, env="EMAILS_FROM_EMAIL")
#     EMAILS_FROM_NAME: Optional[str] = Field(default=None, env="EMAILS_FROM_NAME")

#     # Vector Database Settings
#     QDRANT_URL: str = Field(default="http://localhost:6333", env="QDRANT_URL")
#     QDRANT_API_KEY: Optional[str] = Field(default=None, env="QDRANT_API_KEY")
#     VECTOR_DIMENSION: int = Field(default=384, env="VECTOR_DIMENSION")

#     # Performance Settings
#     MAX_CONCURRENT_REQUESTS: int = Field(default=100, env="MAX_CONCURRENT_REQUESTS")
#     REQUEST_TIMEOUT: int = Field(default=30, env="REQUEST_TIMEOUT")

#     @validator("CORS_ORIGINS", pre=True)
#     def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
#         """Parse CORS origins from environment variable."""
#         if isinstance(v, str) and not v.startswith("["):
#             return [i.strip() for i in v.split(",")]
#         elif isinstance(v, (list, str)):
#             return v
#         raise ValueError(v)

#     @validator("ALLOWED_HOSTS", pre=True)
#     def assemble_allowed_hosts(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
#         """Parse allowed hosts from environment variable."""
#         if isinstance(v, str) and not v.startswith("["):
#             return [i.strip() for i in v.split(",")]
#         elif isinstance(v, (list, str)):
#             return v
#         raise ValueError(v)

#     @validator("SUPPORTED_VIDEO_FORMATS", pre=True)
#     def assemble_video_formats(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
#         """Parse supported video formats from environment variable."""
#         if isinstance(v, str) and not v.startswith("["):
#             return [i.strip() for i in v.split(",")]
#         elif isinstance(v, (list, str)):
#             return v
#         raise ValueError(v)

#     @property
#     def database_url_sync(self) -> str:
#         """Get synchronous database URL for Alembic."""
#         if self.DATABASE_URL:
#             return str(self.DATABASE_URL).replace(
#                 "postgresql+asyncpg://", "postgresql://"
#             )
#         return "postgresql://user:password@localhost/insightstream"

#     @property
#     def is_production(self) -> bool:
#         """Check if running in production environment."""
#         return self.ENVIRONMENT.lower() == "production"

#     @property
#     def is_development(self) -> bool:
#         """Check if running in development environment."""
#         return self.ENVIRONMENT.lower() == "development"

#     @property
#     def is_testing(self) -> bool:
#         """Check if running in testing environment."""
#         return self.ENVIRONMENT.lower() == "testing"

#     class Config:
#         """Pydantic configuration."""

#         env_file = ".env"
#         env_file_encoding = "utf-8"
#         case_sensitive = True


# @lru_cache()
# def get_settings() -> Settings:
#     """
#     Get cached application settings.

#     Returns:
#         Settings: Application configuration instance
#     """
#     return Settings()


# # Export commonly used settings
# settings = get_settings()

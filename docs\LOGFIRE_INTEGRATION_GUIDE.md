# Logfire Integration Guide for InsightStream

## 📋 Overview

This guide provides comprehensive instructions for integrating Pydantic Logfire with the InsightStream backend, enabling complete observability for your FastAPI + SQLModel + AsyncPG stack.

## 🎯 What is Logfire?

[Pydantic Logfire](https://logfire.pydantic.dev/) is an observability platform built by the creators of Pydantic. It provides:

- **Real-time monitoring** of your application performance
- **Distributed tracing** across your entire stack
- **Automatic instrumentation** for popular Python libraries
- **OpenTelemetry compatibility** with industry standards
- **Beautiful dashboards** for visualizing your data

## 🏗️ Architecture Overview

Our Logfire integration covers the complete InsightStream stack:

```mermaid
graph TB
    subgraph "Application Stack"
        A[FastAPI<br/>Web Layer] --> B[SQLModel<br/>ORM Layer]
        B --> C[AsyncPG<br/>Database Layer]
    end

    subgraph "Instrumentation Layer"
        D[FastAPI Traces<br/>• HTTP Requests<br/>• Validation<br/>• Middleware<br/>• Error Handling]
        E[SQLAlchemy Traces<br/>• ORM Queries<br/>• Transactions<br/>• Relationships<br/>• Bulk Operations]
        F[AsyncPG Traces<br/>• SQL Queries<br/>• Connections<br/>• Pool Metrics<br/>• Query Parameters]
    end

    subgraph "External Services"
        G[HTTPX Traces<br/>• HTTP Clients<br/>• API Calls<br/>• Request/Response<br/>• Error Tracking]
    end

    subgraph "System Monitoring"
        H[System Metrics<br/>• CPU Usage<br/>• Memory<br/>• Disk I/O<br/>• Network]
    end

    subgraph "Logfire Cloud"
        I[Unified Dashboard<br/>• Real-time Monitoring<br/>• Performance Analytics<br/>• Error Tracking<br/>• Custom Alerts]
    end

    A -.-> D
    B -.-> E
    C -.-> F
    A -.-> G

    D --> I
    E --> I
    F --> I
    G --> I
    H --> I

    style A fill:#e1f5fe,color:black
    style B fill:#f3e5f5,color:black
    style C fill:#e8f5e8,color:black
    style I fill:#fff3e0,color:black
```

## 📦 Package Installation

### Core Installation

Install Logfire with all necessary integrations for your stack:

```bash
cd backend
uv add 'logfire[fastapi,asyncpg,sqlalchemy,httpx]'
```

### Complete Installation (Recommended)

For full observability including system metrics:

```bash
cd backend
uv add 'logfire[fastapi,asyncpg,sqlalchemy,httpx,system-metrics,pydantic]'
```

### Manual Installation Steps

If you prefer to install step by step:

```bash
cd backend

# Core integrations
uv add 'logfire[fastapi]'      # FastAPI instrumentation
uv add 'logfire[asyncpg]'      # AsyncPG database instrumentation  
uv add 'logfire[sqlalchemy]'   # SQLModel/SQLAlchemy instrumentation
uv add 'logfire[httpx]'        # HTTP client instrumentation

# Optional but recommended
uv add 'logfire[system-metrics]'  # System monitoring
uv add 'logfire[pydantic]'        # Enhanced Pydantic validation tracing
```

## 🔧 Configuration

### Environment Variables

Create or update your `.env` file:

```bash
# Required
LOGFIRE_TOKEN=your_logfire_token_here

# Project Configuration
LOGFIRE_PROJECT_NAME=insightstream
LOGFIRE_SERVICE_NAME=insightstream-backend
LOGFIRE_SERVICE_VERSION=0.0.1
LOGFIRE_ENVIRONMENT=development

# Feature Flags (all default to True)
LOGFIRE_INSTRUMENT_FASTAPI=true
LOGFIRE_INSTRUMENT_ASYNCPG=true
LOGFIRE_INSTRUMENT_SQLMODEL=true
LOGFIRE_INSTRUMENT_HTTPX=true
LOGFIRE_INSTRUMENT_REQUESTS=true

# Privacy Settings (default to False for security)
LOGFIRE_CAPTURE_HEADERS=false
LOGFIRE_CAPTURE_QUERY_PARAMETERS=false
```

### Getting Your Logfire Token

1. Visit [Logfire Dashboard](https://logfire.pydantic.dev/)
2. Sign up or log in with your account
3. Create a new project named "insightstream"
4. Copy the write token from your project settings
5. Add it to your `.env` file as `LOGFIRE_TOKEN`

## 🚀 Integration Details

### FastAPI Integration

**What it monitors:**

- HTTP request/response cycles
- Endpoint argument validation
- Request timing and performance
- Middleware execution
- Error tracking with full context

**Configuration in `main.py`:**

```python
from app.core.logfire_config import instrument_fastapi

app = FastAPI(...)
instrument_fastapi(app)  # Automatically configured
```

### SQLModel Integration

**What it monitors:**

- SQLModel ORM queries
- Database transactions
- Relationship loading
- Bulk operations
- Query optimization insights

**How it works:**

- Uses SQLAlchemy instrumentation (SQLModel is built on SQLAlchemy)
- Automatically traces all database operations
- Captures query parameters and execution times

### AsyncPG Integration

**What it monitors:**

- Raw SQL query execution
- Database connection lifecycle
- Connection pool metrics
- Query parameter binding
- Transaction management

**Benefits:**

- Complete database visibility
- Connection pool health monitoring
- Query performance optimization
- Error tracking with SQL context

### HTTPX Integration

**What it monitors:**

- HTTP client requests to external APIs
- Request/response timing
- Header capture (if enabled)
- Error and retry tracking
- Async and sync client support

## 🧪 Testing Your Integration

### Run the Test Script

```bash
cd backend
python test_logfire_config.py
```

**Expected Output:**

```text
🧪 Testing Logfire Configuration...
📋 Environment: development
📋 Logfire Token: ✅ Set
📋 Project Name: insightstream
📋 Service Name: insightstream-backend
📋 FastAPI Instrumentation: ✅ Enabled
📋 AsyncPG Instrumentation: ✅ Enabled
📋 SQLModel Instrumentation: ✅ Enabled
📋 HTTPX Instrumentation: ✅ Enabled
✅ Logfire configuration successful
🎉 All tests passed! Logfire is properly configured.
```

### Manual Testing

1. **Start the application:**

   ```bash
   cd backend
   python -m app.main
   ```

2. **Make test requests:**

   ```bash
   curl http://localhost:8000/health
   curl http://localhost:8000/ready
   ```

3. **Check Logfire Dashboard:**
   - Visit your Logfire project dashboard
   - You should see traces for HTTP requests
   - Database queries should appear as child spans

## 📊 What You'll See in Logfire

### Request Traces

- **HTTP Requests**: Complete request lifecycle from FastAPI
- **Database Queries**: SQLModel operations and raw SQL from AsyncPG
- **External Calls**: Any HTTPX requests to external services
- **Performance Metrics**: Timing for each operation

### Error Tracking

- **Validation Errors**: FastAPI request validation failures
- **Database Errors**: SQL errors with full context
- **HTTP Errors**: Failed external API calls
- **Application Errors**: Custom exceptions with stack traces

### Performance Insights

- **Slow Queries**: Database operations taking too long
- **Request Bottlenecks**: Slow HTTP endpoints
- **Connection Pool**: Database connection health
- **Memory Usage**: Application resource consumption

## 🔍 Advanced Configuration

### Custom Spans

Create custom spans for specific operations:

```python
from app.core.logfire_config import create_span

async def process_video(video_id: str):
    with create_span("video_processing", video_id=video_id):
        # Your video processing logic
        pass
```

### User Action Tracking

Track user interactions:

```python
from app.core.logfire_config import log_user_action

log_user_action(
    user_id="user_123",
    action="video_upload",
    video_size=1024000,
    video_format="mp4"
)
```

### Performance Monitoring

Log performance metrics:

```python
from app.core.logfire_config import log_performance_metric

log_performance_metric(
    operation="video_transcription",
    duration=45.2,
    video_length=300,
    model="whisper-large"
)
```

## 🛡️ Security and Privacy

### Data Scrubbing

Logfire automatically scrubs sensitive data, but you can configure additional scrubbing:

```python
# In your logfire configuration
logfire.configure(
    scrubbing_patterns=[
        r'password=\w+',
        r'api_key=\w+',
        r'token=\w+'
    ]
)
```

### Header and Query Parameter Capture

By default, headers and query parameters are NOT captured for security. Enable only if needed:

```bash
# Only enable in development
LOGFIRE_CAPTURE_HEADERS=true
LOGFIRE_CAPTURE_QUERY_PARAMETERS=true
```

## 🚨 Troubleshooting

### Common Issues

1. **"Logfire token not provided"**
   - Ensure `LOGFIRE_TOKEN` is set in your `.env` file
   - Check that the token is valid and not expired

2. **"Failed to instrument [component]"**
   - Verify the component's extra is installed: `uv add 'logfire[component]'`
   - Check for version conflicts in dependencies

3. **"No traces appearing in dashboard"**
   - Verify your project name matches the dashboard
   - Check network connectivity to Logfire
   - Ensure the service is making actual requests

### Debug Mode

Enable debug logging to troubleshoot issues:

```bash
LOG_LEVEL=DEBUG python -m app.main
```

### Health Checks

Use the built-in health endpoints to verify integration:

```bash
# Check application health
curl http://localhost:8000/health

# Check database connectivity (includes Logfire logging)
curl http://localhost:8000/ready
```

## 📚 Additional Resources

- [Logfire Documentation](https://logfire.pydantic.dev/docs/)
- [FastAPI Integration](https://logfire.pydantic.dev/docs/integrations/web-frameworks/fastapi/)
- [AsyncPG Integration](https://logfire.pydantic.dev/docs/integrations/databases/asyncpg/)
- [SQLAlchemy Integration](https://logfire.pydantic.dev/docs/integrations/databases/sqlalchemy/)
- [OpenTelemetry Documentation](https://opentelemetry.io/docs/languages/python/)

## 🎉 Conclusion

Your InsightStream backend now has comprehensive observability through Logfire! You can:

- Monitor all HTTP requests and responses
- Track database query performance
- Observe external API calls
- Get real-time alerts on errors
- Analyze performance bottlenecks
- Debug issues with complete context

The integration is production-ready and will scale with your application as it grows.

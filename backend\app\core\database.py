"""
Database Configuration and Connection Management

This module handles database connections, session management, and table creation
using SQLModel with asyncpg for PostgreSQL.
"""

import logging
from typing import Async<PERSON>enerator

import logfire
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel, creat
from sqlmodel.ext.asyncio import AsyncSession

from app.core.config import get_settings

# Get application settings
settings = get_settings()

# Global engine instance
_engine: AsyncEngine | None = None

logger = logging.getLogger(__name__)


def get_engine() -> AsyncEngine:
    """
    Get or create the database engine.

    Returns:
        AsyncEngine: SQLAlchemy async engine instance
    """
    global _engine

    if _engine is None:
        if not settings.DATABASE_URL:
            raise ValueError("DATABASE_URL is not configured")

        # Create async engine with asyncpg
        _engine = create_async_engine(
            str(settings.DATABASE_URL),
            echo=settings.DATABASE_ECHO,
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            pool_pre_ping=True,  # Verify connections before use
            pool_recycle=3600,  # Recycle connections every hour
        )

        logger.info("Database engine created successfully")
        logfire.info(
            "Database engine created",
            database_url=str(settings.DATABASE_URL).split("@")[1]
            if "@" in str(settings.DATABASE_URL)
            else "unknown",
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
        )

    return _engine


# Create async session factory
async_session_factory = sessionmaker(
    class_=AsyncSession,
    bind=get_engine(),
    expire_on_commit=False,
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session.

    Yields:
        AsyncSession: Database session for dependency injection
    """
    async with async_session_factory() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            logfire.error("Database session error", error=str(e))
            raise
        finally:
            await session.close()


async def create_db_and_tables() -> None:
    """
    Create database tables if they don't exist.

    This function should be called during application startup.
    """
    try:
        engine = get_engine()

        # Import all models to ensure they are registered with SQLModel
        # Note: These imports will be added as models are created
        # from app.models.user import User, UserSession, UserNote
        # from app.models.video import Video, Transcript, TranscriptSegment
        # from app.models.ai_interaction import AIInteraction, AIInteractionFollowUp
        # from app.models.voice_interaction import VoiceInteraction
        # from app.models.analytics import UserAnalytics

        async with engine.begin() as conn:
            # Create all tables
            await conn.run_sync(SQLModel.metadata.create_all)

        logger.info("Database tables created successfully")
        logfire.info("Database tables created successfully")

    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        logfire.error("Failed to create database tables", error=str(e))
        raise


async def drop_db_and_tables() -> None:
    """
    Drop all database tables.

    WARNING: This will delete all data! Use only for testing or development.
    """
    if settings.is_production:
        raise RuntimeError("Cannot drop tables in production environment")

    try:
        engine = get_engine()

        async with engine.begin() as conn:
            # Drop all tables
            await conn.run_sync(SQLModel.metadata.drop_all)

        logger.warning("All database tables dropped")
        logfire.warning("All database tables dropped")

    except Exception as e:
        logger.error(f"Failed to drop database tables: {e}")
        logfire.error("Failed to drop database tables", error=str(e))
        raise


async def check_db_connection() -> bool:
    """
    Check if database connection is working.

    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        engine = get_engine()

        async with engine.begin() as conn:
            # Simple query to test connection
            result = await conn.execute("SELECT 1")
            await result.fetchone()

        logger.info("Database connection check successful")
        return True

    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        logfire.error("Database connection check failed", error=str(e))
        return False


class DatabaseManager:
    """
    Database manager for handling connections and transactions.
    """

    def __init__(self):
        self.engine = get_engine()

    async def execute_query(self, query: str, params: Optional[dict] = None) -> any:
        """
        Execute a raw SQL query.

        Args:
            query: SQL query string
            params: Query parameters

        Returns:
            Query result
        """
        async with async_session_factory() as session:
            try:
                result = await session.execute(query, params or {})
                await session.commit()
                return result
            except Exception as e:
                await session.rollback()
                logger.error(f"Query execution failed: {e}")
                logfire.error("Query execution failed", query=query, error=str(e))
                raise

    async def get_table_info(self, table_name: str) -> dict:
        """
        Get information about a database table.

        Args:
            table_name: Name of the table

        Returns:
            dict: Table information
        """
        query = """
        SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default
        FROM information_schema.columns 
        WHERE table_name = :table_name
        ORDER BY ordinal_position;
        """

        try:
            result = await self.execute_query(query, {"table_name": table_name})
            columns = []
            for row in result:
                columns.append(
                    {
                        "name": row[0],
                        "type": row[1],
                        "nullable": row[2] == "YES",
                        "default": row[3],
                    }
                )

            return {
                "table_name": table_name,
                "columns": columns,
            }

        except Exception as e:
            logger.error(f"Failed to get table info for {table_name}: {e}")
            logfire.error(
                "Failed to get table info", table_name=table_name, error=str(e)
            )
            raise

    async def get_database_stats(self) -> dict:
        """
        Get database statistics.

        Returns:
            dict: Database statistics
        """
        queries = {
            "total_tables": "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'",
            "database_size": "SELECT pg_size_pretty(pg_database_size(current_database()))",
            "active_connections": "SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active'",
        }

        stats = {}

        for stat_name, query in queries.items():
            try:
                result = await self.execute_query(query)
                row = await result.fetchone()
                stats[stat_name] = row[0] if row else None
            except Exception as e:
                logger.warning(f"Failed to get {stat_name}: {e}")
                stats[stat_name] = None

        return stats


# Global database manager instance
db_manager = DatabaseManager()
